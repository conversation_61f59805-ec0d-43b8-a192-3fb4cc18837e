<script>
	import { browser } from '$app/environment';
	import DynamicTitle from '$lib/components/DynamicTitle.svelte';
	import EmailSignup from '$lib/components/EmailSignup.svelte';

	const features = [
		{
			name: 'Unified Search',
			description:
				'Find anime, manga, VNs, and LNs across MAL, AniList, Kitsu, VNDB and more from one place.',
			icon: '🔍' // Placeholder icon
		},
		{
			name: 'Centralized Lists',
			description: 'Manage your watch, read, and play lists for all services seamlessly.',
			icon: '📚' // Placeholder icon
		},
		{
			name: 'Track Friends Aniwhere',
			description:
				"See what your friends are watching and reading across all platforms with real-time activity feeds.",
			icon: '👀' // Placeholder icon
		}
	];
	$: title = 'Home - Anithing.moe';
	$: description =
		'Anithing.moe is your central hub for Japanese media. Search anime, manga, VNs, and LNs across MAL, AniList, Kitsu, VNDB, and more. Manage your lists and track friends activities seamlessly.';
	$: imageUrl = 'https://pixeldrain.com/api/file/nzfyjq8f'; // Assuming this is a relevant image for Anithing.moe
	$: canonicalUrl = browser ? window.location.href : 'https://anithing.moe/';
</script>

<svelte:head>
	<!-- Basic metadata -->
	<title>{title}</title>
	<meta name="description" content={description} />

	<!-- OpenGraph tags -->
	<meta property="og:title" content={title} />
	<meta property="og:description" content={description} />
	<meta property="og:image" content={imageUrl} />
	<meta property="og:image:width" content="1200" />
	<meta property="og:image:height" content="630" />
	<meta property="og:image:alt" content="anithing.moe - Unified Japanese Media Hub" />
	<meta property="og:url" content={canonicalUrl} />
	<meta property="og:type" content="website" />
	<meta property="og:site_name" content="anithing.moe" />

	<!-- Twitter Card tags -->
	<meta name="twitter:card" content="summary_large_image" />
	<meta name="twitter:site" content="anithing_moe" />
	<meta name="twitter:title" content={title} />
	<meta name="twitter:description" content={description} />
	<meta name="twitter:image" content={imageUrl} />
	<meta name="twitter:image:alt" content="anithing.moe - Unified Japanese Media Hub" />

	<!-- Canonical URL -->
	<link rel="canonical" href={canonicalUrl} />

	<!-- Additional SEO metadata -->
	<meta
		name="keywords"
		content="anime, manga, visual novel, light novel, MAL, AniList, Kitsu, VNDB, unified search, media tracking, anithing, anithing.moe, japanese media"
	/>
	<meta name="theme-color" content="#ee8585" />
	<meta name="author" content="Anithing.moe" />
	<meta name="robots" content="index, follow" />
</svelte:head>

<div
	class="flex min-h-screen flex-col items-center justify-center px-3 py-10 sm:px-6 sm:py-16 lg:px-8"
>
	<header class="mb-8 sm:mb-10 md:mb-16">
		<div class="flex flex-col items-center justify-center">
			<DynamicTitle />
			<p
				class="mx-auto mt-3 max-w-2xl px-1 text-center text-base text-slate-300 sm:mt-4 sm:text-lg md:text-xl"
			>
				Your ultimate gateway to the world of Japanese media. Search, track, and manage all your
				lists in one unified experience.
			</p>
		</div>
	</header>

	<main class="w-full max-w-4xl">
		<!-- Features Section -->
		<section class="mb-12 md:mb-16">
			<h2 class="mb-6 text-center text-2xl font-semibold text-sky-400 sm:mb-8 sm:text-3xl">
				What Anithing.moe Offers
			</h2>
			<div class="grid grid-cols-1 gap-5 sm:grid-cols-2 sm:gap-6 md:grid-cols-3 md:gap-8">
				{#each features as feature}
					<div
						class="rounded-xl border border-gray-700/50 bg-gray-800/50 p-4 shadow-lg backdrop-blur-sm sm:p-6"
					>
						<div class="mb-3 text-3xl sm:mb-4 sm:text-4xl">{feature.icon}</div>
						<h3 class="mb-2 text-lg font-semibold text-sky-300 sm:text-xl">{feature.name}</h3>
						<p class="text-sm text-slate-300">{feature.description}</p>
					</div>
				{/each}
			</div>
		</section>

		<!-- Email Signup Section -->
		<section class="flex flex-col items-center">
			<EmailSignup />
		</section>
	</main>

	<footer class="mt-16 text-center text-sm text-slate-400">
		<p>© {new Date().getFullYear()} Anithing.moe - All your media, aniwhere.</p>
	</footer>
</div>